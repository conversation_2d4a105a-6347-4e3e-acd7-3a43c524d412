{"Login": {"Username": "şirin2", "Password": "3clWdtSv", "LoginUrl": "https://alfasystemsonline.com/panelx/", "ListeleUrl": "https://alfasystemsonline.com/panelx/ajax/listele_cekim_havuz.php", "PaneleCekUrl": "https://alfasystemsonline.com/panelx/ajax/panele_cek_islem.php"}, "Bot": {"PollingIntervalMs": 100, "RequestTimeoutMs": 10000, "MaxRetryCount": 3, "EnableDetailedLogging": true, "TutarFiltre": {"Enabled": true, "MinTutar": 3000, "MaxTutar": 10000, "IgnoreInvalidAmounts": true}, "CekimLimitleri": {"MaxKayitSayisi": 1, "MaxToplamTutar": 500000, "ResetDaily": true}}, "Proxy": {"Enabled": false, "Host": "", "Port": 0, "Username": "", "Password": ""}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "PaneleCekmeBot": "Debug", "PaneleCekmeBot.Services": "Debug"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/panele-cekme-bot-.log", "rollingInterval": "Day", "retainedFileCountLimit": 1000, "fileSizeLimitBytes": 10000000, "rollOnFileSizeLimit": true, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff} {Level:u3}] {SourceContext} {Message:lj}{NewLine}{Exception}"}}]}}