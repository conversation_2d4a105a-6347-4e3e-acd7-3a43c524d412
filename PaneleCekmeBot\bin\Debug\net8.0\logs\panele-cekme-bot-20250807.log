[2025-08-07 01:04:03.013 INF]  🚀 <PERSON><PERSON><PERSON><PERSON>a başlatılıyor...
[2025-08-07 01:04:03.268 INF]  📁 Log dosyaları: logs/ klasör<PERSON>ne kaydediliyor
[2025-08-07 01:04:03.560 FTL]  💥 Uygulama kritik hata ile sonlandı
System.IO.FileNotFoundException: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'C:\temp\PANELE-CEKME\PaneleCekmeBot\bin\Debug\net8.0\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.HandleException(ExceptionDispatchInfo info)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load()
   at Microsoft.Extensions.Configuration.ConfigurationRoot..ctor(IList`1 providers)
   at Microsoft.Extensions.Configuration.ConfigurationBuilder.Build()
   at Microsoft.Extensions.Hosting.HostBuilder.InitializeAppConfiguration()
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at PaneleCekmeBot.Program.Main(String[] args) in C:\temp\PANELE-CEKME\PaneleCekmeBot\Program.cs:line 41
